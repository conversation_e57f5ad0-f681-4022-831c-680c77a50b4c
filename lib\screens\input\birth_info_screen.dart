// lib/screens/input/birth_info_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/birth_data.dart';
import '../../models/location.dart';
import '../../models/chart_data.dart';
import '../../models/palace.dart';
import '../../services/storage_service.dart';
import '../../models/advanced_settings.dart';
import '../../services/settings_service.dart';
import '../../data/china_cities.dart';
import '../../services/ziwei_chart_calculator.dart';
import '../../models/ziwei_chart.dart';
import '../../services/paipan_service.dart';
import '../../widgets/custom_time_selector.dart';

class BirthInfoScreen extends StatefulWidget {
  @override
  _BirthInfoScreenState createState() => _BirthInfoScreenState();
}

class _BirthInfoScreenState extends State<BirthInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  
  String _gender = 'male';
  bool _isLunar = false;
  DateTime _birthDate = DateTime.now();
  TimeOfDay _birthTime = TimeOfDay.now();
  bool _isLeapMonth = false;
  bool _isLoading = false;
  bool _showAdvancedSettings = false; // 控制是否显示高级设置

  late Location _birthLocation;
  AdvancedSettings _advancedSettings = AdvancedSettings.defaultSettings();
  List<Location> _nearbyCities = []; // 附近城市列表

  // 计算结果缓存 - 仅保留四柱显示需要的数据
  Map<String, String>? _fourPillars;

  // 排盘服务
  final PaipanService _paipanService = PaipanService();

  @override
  void initState() {
    super.initState();
    _birthLocation = Location(
      country: '中国',
      province: '北京市',
      city: '北京市',
      latitude: 39.9042,
      longitude: 116.4074,
    );
    _initializeSettings(); // 初始化设置
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  /// 初始化设置
  Future<void> _initializeSettings() async {
    try {
      _advancedSettings = await SettingsService.instance.loadAdvancedSettings();
      setState(() {
        _updateCalculations(); // 设置加载完成后再计算
      });
    } catch (e) {
      print('加载设置失败: $e');
      // 使用默认设置
      _advancedSettings = AdvancedSettings.defaultSettings();
      setState(() {
        _updateCalculations();
      });
    }
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    try {
      await SettingsService.instance.saveAdvancedSettings(_advancedSettings);
    } catch (e) {
      print('保存设置失败: $e');
    }
  }

  /// 更新四柱计算结果
  void _updateCalculations() {
    // 使用异步计算避免阻塞UI
    Future.microtask(() => _performCalculations());
  }

  /// 执行四柱计算逻辑
  Future<void> _performCalculations() async {
    try {
      // 确保必要的变量已初始化
      if (_birthDate == null || _birthTime == null) {
        _fourPillars = null;
        if (mounted) setState(() {});
        return;
      }

      // 如果是农历，先转换为公历
      DateTime solarDate = _birthDate;
      if (_isLunar) {
        List<int>? result = _paipanService.lunar2Solar(_birthDate.year, _birthDate.month, _birthDate.day, _isLeapMonth);
        if (result != null && result.length >= 3) {
          solarDate = DateTime(result[0], result[1], result[2]);
        }
      }

      // 使用排盘服务计算四柱干支
      Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(
        solarDate.year,
        solarDate.month,
        solarDate.day,
        _birthTime.hour,
        _birthTime.minute
      );

      if (ganZhiResult.isNotEmpty) {
        List<int> tg = ganZhiResult['tg'];
        List<int> dz = ganZhiResult['dz'];

        _fourPillars = {
          'year': '${_paipanService.ctg[tg[0]]}${_paipanService.cdz[dz[0]]}',
          'month': '${_paipanService.ctg[tg[1]]}${_paipanService.cdz[dz[1]]}',
          'day': '${_paipanService.ctg[tg[2]]}${_paipanService.cdz[dz[2]]}',
          'hour': '${_paipanService.ctg[tg[3]]}${_paipanService.cdz[dz[3]]}',
        };
      } else {
        _fourPillars = null;
      }

      // 更新附近城市
      _updateNearbyCities();

      if (mounted) setState(() {});

    } catch (e) {
      print('计算四柱干支失败: $e');
      _fourPillars = null;
      if (mounted) setState(() {});
    }
  }

  /// 更新附近城市列表
  void _updateNearbyCities() {
    _nearbyCities = ChinaCitiesDatabase.getCitiesByLongitude(_birthLocation.longitude);
    // 移除当前选中的城市
    _nearbyCities.removeWhere((city) => city.city == _birthLocation.city);
  }

  /// 显示城市选择器
  void _showCitySelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('选择出生地点'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              // 搜索框
              TextField(
                decoration: InputDecoration(
                  labelText: '搜索城市',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  // 实现搜索功能
                },
              ),
              SizedBox(height: 16),

              // 城市列表
              Expanded(
                child: ListView.builder(
                  itemCount: ChinaCitiesDatabase.getAllCities().length,
                  itemBuilder: (context, index) {
                    Location city = ChinaCitiesDatabase.getAllCities()[index];
                    return ListTile(
                      title: Text(city.city),
                      subtitle: Text('${city.province} - 经度: ${city.longitude.toStringAsFixed(2)}°'),
                      onTap: () {
                        _selectCity(city);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 选择城市
  void _selectCity(Location city) {
    print('地点选择变更: ${_birthLocation.name} -> ${city.name}');

    if (mounted) {
      setState(() {
        _birthLocation = city;
      });
    }

    // 延迟执行计算，确保状态更新完成
    Future.delayed(Duration(milliseconds: 50)).then((_) {
      if (mounted) {
        _updateCalculations();
      }
    });
  }




  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('出生信息'),
        actions: [
          IconButton(
            icon: Icon(Icons.help_outline),
            onPressed: _showHelp,
            tooltip: '帮助',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 主要信息区域 - 紧凑布局
              _buildCompactInfoCard(),
              SizedBox(height: 16),

              // 天干地支信息 - 默认显示
              _buildStemBranchCard(),
              SizedBox(height: 16),

              // 高级设置切换
              _buildAdvancedSettingsToggle(),
              if (_showAdvancedSettings) ...[
                SizedBox(height: 16),
                _buildAdvancedSettingsCard(),
              ],

              SizedBox(height: 32),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _generateChart,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: _isLoading
                  ? CircularProgressIndicator(color: Colors.white)
                  : Text(
                      '生成命盘',
                      style: TextStyle(fontSize: 18),
                    ),
            ),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ),
    );
  }


  /// 构建紧凑的信息卡片
  Widget _buildCompactInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 姓名和性别行
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: '姓名',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _gender,
                    decoration: InputDecoration(
                      labelText: '性别',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: [
                      DropdownMenuItem(value: 'male', child: Text('男')),
                      DropdownMenuItem(value: 'female', child: Text('女')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _gender = value!;
                      });
                    },
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // 日期和历法选择行
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: InkWell(
                    onTap: () => _selectDate(context),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: '出生日期',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      child: Text(
                        '${_birthDate.year}年${_birthDate.month}月${_birthDate.day}日',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Text('公历', style: TextStyle(fontSize: 12)),
                      ),
                      Switch(
                        value: _isLunar,
                        onChanged: (value) {
                          print('历法切换(紧凑): ${_isLunar ? "农历" : "公历"} -> ${value ? "农历" : "公历"}');

                          if (mounted) {
                            setState(() {
                              _isLunar = value;
                            });
                          }

                          // 延迟执行计算
                          Future.delayed(Duration(milliseconds: 50)).then((_) {
                            if (mounted) {
                              _updateCalculations();
                            }
                          });
                        },
                      ),
                      Expanded(
                        child: Text('农历', style: TextStyle(fontSize: 12)),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // 时间选择行
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectTime(context),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: '出生时间',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      child: Text(
                        '${_birthTime.hour.toString().padLeft(2, '0')}:${_birthTime.minute.toString().padLeft(2, '0')}',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '时辰信息',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      Text(
                        '点击时间选择器查看',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // 地点选择行
            _buildLocationSelector(),
          ],
        ),
      ),
    );
  }

  /// 构建地点选择器
  Widget _buildLocationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              flex: 2,
              child: InkWell(
                onTap: () => _showCitySelector(),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: '出生地点',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  child: Text(
                    _birthLocation.name,
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: '经度',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: Text(
                  '${_birthLocation.longitude.toStringAsFixed(2)}°E',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),

        // 显示附近城市
        if (_nearbyCities.isNotEmpty) ...[
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '附近城市（±2°）：',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                SizedBox(height: 4),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: _nearbyCities.take(5).map((city) => InkWell(
                    onTap: () => _selectCity(city),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Text(
                        '${city.city} (${city.longitude.toStringAsFixed(1)}°)',
                        style: TextStyle(fontSize: 11, color: Colors.blue[700]),
                      ),
                    ),
                  )).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }



  Widget _buildStemBranchCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calendar_view_day, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  '四柱干支',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            if (_fourPillars != null) ...[
              Row(
                children: [
                  Expanded(
                    child: _buildPillarItem('年柱', _fourPillars!['year']!, Colors.red),
                  ),
                  Expanded(
                    child: _buildPillarItem('月柱', _fourPillars!['month']!, Colors.green),
                  ),
                  Expanded(
                    child: _buildPillarItem('日柱', _fourPillars!['day']!, Colors.blue),
                  ),
                  Expanded(
                    child: _buildPillarItem('时柱', _fourPillars!['hour']!, Colors.purple),
                  ),
                ],
              ),
            ] else ...[
              Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    '请完善出生信息以开始计算',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPillarItem(String title, String value, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 4),
        Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }



  Widget _buildAdvancedSettingsToggle() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '高级设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '年柱起始、子时处理、闰月分界等设置',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            Switch(
              value: _showAdvancedSettings,
              onChanged: (value) {
                setState(() {
                  _showAdvancedSettings = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettingsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.deepPurple),
                SizedBox(width: 8),
                Text(
                  '高级设置选项',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // 年柱起始点选择
            _buildSettingItem(
              '年柱起始点',
              _advancedSettings.yearPillarStartPointName,
              _advancedSettings.yearPillarStartPointDescription,
              () => _showYearPillarStartPointDialog(),
            ),

            Divider(),

            // 子时处理方式
            _buildSettingItem(
              '子时处理方式',
              _advancedSettings.ziHourModeName,
              _advancedSettings.ziHourModeDescription,
              () => _showZiHourModeDialog(),
            ),

            Divider(),

            // 闰月分界处理
            _buildSettingItem(
              '闰月分界处理',
              _advancedSettings.leapMonthBoundaryName,
              _advancedSettings.leapMonthBoundaryDescription,
              () => _showLeapMonthBoundaryDialog(),
            ),

            Divider(),

            // 真太阳时开关
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _advancedSettings.useApparentSolarTime ? Colors.orange[50] : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _advancedSettings.useApparentSolarTime ? Colors.orange[200]! : Colors.grey[300]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.wb_sunny,
                    color: _advancedSettings.useApparentSolarTime ? Colors.orange[600] : Colors.grey[500],
                    size: 24,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '使用真太阳时',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: _advancedSettings.useApparentSolarTime ? Colors.orange[700] : Colors.grey[700],
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '打开后自动根据出生地经度计算时差，并以真太阳时计算四柱干支',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(height: 4),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red[100],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '重要：四柱干支是紫微斗数排盘的关键基础数据',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.red[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 8),
                  Switch(
                    value: _advancedSettings.useApparentSolarTime,
                    onChanged: (value) {
                      setState(() {
                        _advancedSettings = _advancedSettings.copyWith(
                          useApparentSolarTime: value,
                        );
                        _saveSettings();
                      });
                      // 延迟执行计算，确保状态更新完成
                      Future.microtask(() {
                        _updateCalculations();
                      });
                    },
                    activeColor: Colors.orange[600],
                    activeTrackColor: Colors.orange[200],
                  ),
                ],
              ),
            ),

            Divider(),

            // 节气信息开关
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '显示节气信息',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '显示最近的二十四节气信息',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _advancedSettings.showSolarTerms,
                  onChanged: (value) {
                    setState(() {
                      _advancedSettings = _advancedSettings.copyWith(
                        showSolarTerms: value,
                      );
                      _saveSettings();
                      _updateCalculations();
                    });
                  },
                ),
              ],
            ),

            SizedBox(height: 16),

            // 重置按钮
            Center(
              child: TextButton.icon(
                onPressed: () => _resetAdvancedSettings(),
                icon: Icon(Icons.refresh),
                label: Text('重置为默认设置'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(String title, String value, String description, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 4),
                  Text(
                    value,
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }







  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _birthDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.orange,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _birthDate) {
      print('日期选择变更: ${_birthDate} -> $picked');

      // 立即更新状态
      if (mounted) {
        setState(() {
          _birthDate = picked;
        });
      }

      // 延迟执行计算，确保状态更新完成
      await Future.delayed(Duration(milliseconds: 50));
      if (mounted) {
        _updateCalculations();
      }
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => CustomTimeSelector(
        initialDate: _birthDate,
        initialTime: _birthTime,
        isLunar: _isLunar,
        isLeapMonth: _isLeapMonth,
      ),
    );

    if (result != null) {
      print('时间选择变更: ${_birthDate} ${_birthTime.format(context)} -> ${result['date']} ${result['time']}');

      // 立即更新状态
      if (mounted) {
        setState(() {
          _birthDate = result['date'];
          _birthTime = result['time'];
          _isLunar = result['isLunar'];
          _isLeapMonth = result['isLeapMonth'];
        });
      }

      // 延迟执行计算，确保状态更新完成
      await Future.delayed(Duration(milliseconds: 50));
      if (mounted) {
        _updateCalculations();
      }
    }
  }

  /// 显示年柱起始点选择对话框
  void _showYearPillarStartPointDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('年柱起始点选择'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: YearPillarStartPoint.values.map((option) {
            return RadioListTile<YearPillarStartPoint>(
              title: Text(_getYearPillarStartPointName(option)),
              subtitle: Text(_getYearPillarStartPointDescription(option)),
              value: option,
              groupValue: _advancedSettings.yearPillarStartPoint,
              onChanged: (value) {
                setState(() {
                  _advancedSettings = _advancedSettings.copyWith(
                    yearPillarStartPoint: value,
                  );
                  _saveSettings();
                  _updateCalculations();
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 显示子时处理方式选择对话框
  void _showZiHourModeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('子时处理方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ZiHourMode.values.map((option) {
            return RadioListTile<ZiHourMode>(
              title: Text(_getZiHourModeName(option)),
              subtitle: Text(_getZiHourModeDescription(option)),
              value: option,
              groupValue: _advancedSettings.ziHourMode,
              onChanged: (value) {
                setState(() {
                  _advancedSettings = _advancedSettings.copyWith(
                    ziHourMode: value,
                  );
                  _saveSettings();
                  _updateCalculations();
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 显示闰月分界处理选择对话框
  void _showLeapMonthBoundaryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('闰月分界处理'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: LeapMonthBoundary.values.map((option) {
            return RadioListTile<LeapMonthBoundary>(
              title: Text(_getLeapMonthBoundaryName(option)),
              subtitle: Text(_getLeapMonthBoundaryDescription(option)),
              value: option,
              groupValue: _advancedSettings.leapMonthBoundary,
              onChanged: (value) {
                setState(() {
                  _advancedSettings = _advancedSettings.copyWith(
                    leapMonthBoundary: value,
                  );
                  _saveSettings();
                  _updateCalculations();
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 重置高级设置
  void _resetAdvancedSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('重置设置'),
        content: Text('确定要重置所有高级设置为默认值吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _advancedSettings = AdvancedSettings.defaultSettings();
                _saveSettings();
                _updateCalculations();
              });
              Navigator.pop(context);
            },
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  Future<void> _generateChart() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final birthData = BirthData(
        name: _nameController.text.trim(),
        gender: _gender,
        calendar: _isLunar ? 'lunar' : 'solar',
        year: _birthDate.year,
        month: _birthDate.month,
        day: _birthDate.day,
        hour: _birthTime.hour,
        minute: _birthTime.minute,
        isLeapMonth: _isLeapMonth,
        location: _birthLocation,
      );

      // 计算紫微斗数命盘
      ZiweiChart ziweiChart = ZiweiChartCalculator.calculateChart(birthData, _advancedSettings);

      // 转换为ChartData格式保存
      final storageService = Provider.of<StorageService>(context, listen: false);
      final chartData = ChartData(
        id: ziweiChart.id,
        name: ziweiChart.name,
        gender: ziweiChart.gender,
        birthData: ziweiChart.birthData,
        ascendant: ziweiChart.lifePalaceIndex,
        bodyPalace: ziweiChart.bodyPalaceIndex,
        palaces: ziweiChart.palaces.map((ziweiPalace) => Palace(
          index: ziweiPalace.index,
          name: ziweiPalace.name,
          earthBranch: ziweiPalace.earthlyBranch,
          mainStars: ziweiPalace.mainStars.map((star) => star.name).toList(),
          minorStars: ziweiPalace.auxiliaryStars.map((star) => star.name).toList(),
          transformStars: ziweiPalace.transformStars.map((star) => star.name).toList(),
        )).toList(),
        createdAt: ziweiChart.createdAt,
      );
      await storageService.saveChart(chartData);

      // 跳转到命盘展示页面
      Navigator.pushReplacementNamed(
        context,
        '/chart',
        arguments: ziweiChart,
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建命盘失败：$e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 获取年柱起始点名称
  String _getYearPillarStartPointName(YearPillarStartPoint option) {
    switch (option) {
      case YearPillarStartPoint.lichun:
        return '以立春为新年';
      case YearPillarStartPoint.lunarNewYear:
        return '以正月初一为新年';
    }
  }

  /// 获取年柱起始点描述
  String _getYearPillarStartPointDescription(YearPillarStartPoint option) {
    switch (option) {
      case YearPillarStartPoint.lichun:
        return '符合传统命理学标准，年柱干支在立春时刻发生变化';
      case YearPillarStartPoint.lunarNewYear:
        return '年柱干支在农历新年零时发生变化';
    }
  }

  /// 获取子时处理方式名称
  String _getZiHourModeName(ZiHourMode option) {
    switch (option) {
      case ZiHourMode.early:
        return '早子时';
      case ZiHourMode.late:
        return '晚子时';
    }
  }

  /// 获取子时处理方式描述
  String _getZiHourModeDescription(ZiHourMode option) {
    switch (option) {
      case ZiHourMode.early:
        return '23:00-01:00算作第二天，即23:00后日柱干支按次日计算';
      case ZiHourMode.late:
        return '23:00-24:00算作当天，00:00-01:00算作第二天';
    }
  }

  /// 获取闰月分界处理方式名称
  String _getLeapMonthBoundaryName(LeapMonthBoundary option) {
    switch (option) {
      case LeapMonthBoundary.middle:
        return '闰月月中分界';
      case LeapMonthBoundary.previous:
        return '上月闰月';
      case LeapMonthBoundary.next:
        return '下月闰月';
    }
  }

  /// 获取闰月分界处理方式描述
  String _getLeapMonthBoundaryDescription(LeapMonthBoundary option) {
    switch (option) {
      case LeapMonthBoundary.middle:
        return '闰月的前半月算上个月，后半月算下个月';
      case LeapMonthBoundary.previous:
        return '整个闰月都算作上个月';
      case LeapMonthBoundary.next:
        return '整个闰月都算作下个月';
    }
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('填写说明'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('1. 姓名为选填项，仅用于区分不同命盘'),
              SizedBox(height: 8),
              Text('2. 性别会影响部分宫位的解读'),
              SizedBox(height: 8),
              Text('3. 可选择公历或农历日期'),
              SizedBox(height: 8),
              Text('4. 时辰选择非常重要，它决定了命宫的位置'),
              SizedBox(height: 8),
              Text('5. 出生地点会影响部分星曜的落宫'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('了解了'),
          ),
        ],
      ),
    );
  }
  
  void _showHourInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('时辰对照表'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('子时：23:00-01:00（夜半）'),
              Text('丑时：01:00-03:00（鸡鸣）'),
              Text('寅时：03:00-05:00（平旦）'),
              Text('卯时：05:00-07:00（日出）'),
              Text('辰时：07:00-09:00（食时）'),
              Text('巳时：09:00-11:00（隅中）'),
              Text('午时：11:00-13:00（日中）'),
              Text('未时：13:00-15:00（日昳）'),
              Text('申时：15:00-17:00（哺时）'),
              Text('酉时：17:00-19:00（日入）'),
              Text('戌时：19:00-21:00（黄昏）'),
              Text('亥时：21:00-23:00（人定）'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
}

